<?php

use console\components\dump\Migration;

/**
 * Class m221205_113237_0_table_meeting
 * @property \yii\db\Transaction $_transaction
 */
class m221205_113237_0_table_meeting extends Migration
{
    /**
     * @inheritdoc
     */
    public function safeUp()
    {
        
        $this->runSuccess['createTable'] = $this->createTable('{{%meeting}}', [
            'id' => $this->integer(11)->notNull(),
            'uid' => $this->string(38)->notNull(),
            'date' => $this->date()->null(),
            'name' => $this->string(255)->notNull(),
            'name_en' => $this->string(255)->null(),
        ], $this->tableOptions);

    }

    /**
     * @inheritdoc
     */
    public function safeDown()
    {
        
        foreach ($this->runSuccess as $keyName => $value) {
            if ('createTable' === $keyName) {
                $this->dropTable('{{%meeting}}');
            } elseif ('addTableComment' === $keyName) {
                $this->dropCommentFromTable('{{%meeting}}');
            } else {
                throw new \yii\db\Exception('only support "dropTable" and "dropCommentFromTable"');
            }
        }
    }
}
