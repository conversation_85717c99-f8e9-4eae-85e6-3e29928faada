<?php

namespace console\components;

use common\services\EnvService;
use frontend\jobs\ExportJob;
use Yii;

class <PERSON>rrorHandler extends \yii\console\ErrorHandler
{


    public function renderException($exception)
    {
        dump(memory_get_usage());
        dump($exception);
        if (ExportJob::$cacheErrorkey && ExportJob::$cacheStatuskey) {
            Yii::$app->cache->set(ExportJob::$cacheErrorkey, 'Job failed due to fatal error.');
            Yii::$app->cache->set(ExportJob::$cacheStatuskey, 'error');
            dump(ExportJob::$cacheErrorkey);
            dump(ExportJob::$cacheStatuskey);
            dump('Job failed due to fatal error.');
            $var = Yii::$app->cache->get(ExportJob::$cacheStatuskey);
            dump($var);
        }
    }
}